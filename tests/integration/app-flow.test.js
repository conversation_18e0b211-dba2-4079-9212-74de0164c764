/**
 * Tests d'intégration pour le flux complet de l'application
 */

const { testServerDiscovery } = require('../unit/server-discovery.test');
const { testOdooAuth } = require('../unit/odoo-auth.test');

async function testCompleteFlow() {
  console.log('🧪 === TEST FLUX COMPLET APPLICATION ===');
  
  try {
    console.log('\n1️⃣ === TEST DÉCOUVERTE SERVEURS ===');
    await testServerDiscovery();
    
    console.log('\n2️⃣ === TEST AUTHENTIFICATION ===');
    await testOdooAuth();
    
    console.log('\n✅ === FLUX COMPLET TESTÉ ===');
    console.log('Tous les composants fonctionnent correctement !');
    
  } catch (error) {
    console.log('💥 Erreur dans le flux complet:', error.message);
  }
}

// Exporter pour utilisation en tant que module
module.exports = { testCompleteFlow };

// Exécuter si appelé directement
if (require.main === module) {
  testCompleteFlow();
}
