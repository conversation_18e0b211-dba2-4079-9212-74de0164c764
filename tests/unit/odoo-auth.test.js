/**
 * Tests unitaires pour le module d'authentification Odoo 13
 */

const OdooAuth = require('../../src/main/modules/odoo-auth');

async function testOdooAuth() {
  console.log('🧪 === TEST AUTHENTIFICATION ODOO 13 ===');
  
  try {
    const auth = new OdooAuth();
    
    // Configuration de test (à adapter selon vos besoins)
    const testConfig = {
      email: 'admin',
      password: 'admin',
      serverUrl: 'http://192.168.100.27:8069' // Adapter selon votre configuration
    };
    
    console.log(`🔐 Test d'authentification sur: ${testConfig.serverUrl}`);
    console.log(`👤 Utilisateur: ${testConfig.email}`);
    
    const result = await auth.authenticateOdoo13Direct(
      testConfig.email,
      testConfig.password,
      testConfig.serverUrl
    );
    
    console.log('\n🎯 === RÉSULTAT ===');
    if (result.success) {
      console.log('✅ Authentification réussie !');
      console.log(`👤 Nom d'utilisateur: ${result.name}`);
      console.log(`🆔 ID utilisateur: ${result.userId}`);
      console.log(`🗄️ Base de données: ${result.dbName}`);
      console.log(`🔑 Session ID: ${result.sessionId.substring(0, 20)}...`);
    } else {
      console.log('❌ Authentification échouée');
      console.log(`💥 Erreur: ${result.error}`);
    }
    
  } catch (error) {
    console.log('💥 Erreur critique:', error.message);
  }
}

// Exporter pour utilisation en tant que module
module.exports = { testOdooAuth };

// Exécuter si appelé directement
if (require.main === module) {
  testOdooAuth();
}
