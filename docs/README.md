# 🚀 Edara Electron App

Application Electron pour encapsuler le service Odoo 13 Edara avec découverte automatique des serveurs et authentification directe.

## ✨ Fonctionnalités

- **🔍 Découverte automatique des serveurs** : Détection intelligente des serveurs locaux et distants
- **🎯 Priorité locale** : Préférence automatique pour les serveurs locaux (plus rapides)
- **🔐 Authentification directe Odoo 13** : Connexion sans page de login
- **🚪 Gestion des déconnexions** : Retour automatique à l'interface de l'application
- **🎨 Interface adaptative** : Logos adaptatifs selon le mode sombre/clair
- **⚡ Splash screen intelligent** : Feedback visuel pendant la découverte

## 🏗️ Architecture

```
src/
├── main/                    # Processus principal Electron
│   ├── main.js             # Point d'entrée principal
│   ├── preload.js          # Script de préchargement
│   ├── modules/            # Modules métier
│   │   ├── server-discovery.js    # Découverte des serveurs
│   │   ├── odoo-auth.js           # Authentification Odoo 13
│   │   ├── connection-manager.js  # Gestion des connexions
│   │   └── window-manager.js      # Gestion des fenêtres
│   └── config/             # Configuration
│       └── servers.txt     # Configuration des serveurs
├── renderer/               # Interface utilisateur
│   ├── views/              # Vues/Pages
│   │   └── splash.html     # Page splash
│   ├── assets/             # Ressources statiques
│   │   └── images/         # Images et logos
│   └── scripts/            # Scripts renderer
└── shared/                 # Code partagé
```

## 🚀 Installation

1. **Cloner le projet**
```bash
git clone <repository-url>
cd edara-electron-app
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configurer les serveurs**
Éditer `src/main/config/servers.txt` :
```
# Serveurs locaux (priorité haute)
**************
************
************

# Serveur distant (fallback)
https://edara.ligne-digitale.com
```

## 🎮 Utilisation

### Démarrage de l'application
```bash
npm start
```

### Tests
```bash
# Test découverte des serveurs
node tests/unit/server-discovery.test.js

# Test authentification Odoo
node tests/unit/odoo-auth.test.js

# Test flux complet
node tests/integration/app-flow.test.js
```

### Build
```bash
npm run build
```

## ⚙️ Configuration

### Serveurs
Le fichier `src/main/config/servers.txt` contient la configuration des serveurs :
- **Serveurs locaux** : Adresses IP (ex: `************`)
- **Serveurs distants** : URLs complètes (ex: `https://example.com`)
- **Commentaires** : Lignes commençant par `#`

### Logos
Les logos sont dans `src/renderer/assets/images/` :
- `logo-edara-claire.png` : Logo clair (pour fond sombre)
- `logo-edara-noire.png` : Logo sombre (pour fond clair)
- `edara_illustration.svg` : Illustration vectorielle

## 🔧 Développement

### Structure des modules
- **server-discovery.js** : Découverte et test des serveurs
- **odoo-auth.js** : Authentification Odoo 13 directe
- **connection-manager.js** : Gestion des connexions et serveurs préférés
- **window-manager.js** : Gestion des fenêtres et vues

### Flux de l'application
1. **Splash screen** avec découverte automatique
2. **Test des serveurs** en parallèle (5s timeout)
3. **Sélection du meilleur serveur** (priorité locale)
4. **Interface de connexion** avec serveur optimal
5. **Authentification Odoo 13** directe
6. **Interface Odoo** sans page de login

## 📋 Scripts disponibles

- `npm start` : Démarrer l'application
- `npm run build` : Construire l'application
- `npm test` : Lancer tous les tests

## 🐛 Dépannage

### Problèmes courants
1. **Aucun serveur trouvé** : Vérifier `servers.txt` et la connectivité réseau
2. **Authentification échouée** : Vérifier les identifiants et la version Odoo
3. **Interface ne se charge pas** : Vérifier les logs dans la console

### Logs
Les logs sont disponibles dans la console de développement (F12).

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour toute question ou problème, ouvrir une issue sur GitHub.
