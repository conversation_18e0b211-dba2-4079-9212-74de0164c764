# 📦 Guide d'Installation - Edara Electron App

## 🔧 Prérequis

### Système
- **Node.js** : Version 16.x ou supérieure
- **npm** : Version 8.x ou supérieure
- **Git** : Pour cloner le repository

### Vérification des prérequis
```bash
node --version    # v16.x.x ou supérieur
npm --version     # 8.x.x ou supérieur
git --version     # 2.x.x ou supérieur
```

## 🚀 Installation

### 1. <PERSON><PERSON><PERSON> le projet
```bash
git clone <repository-url>
cd edara-electron-app
```

### 2. Installer les dépendances
```bash
npm install
```

### 3. Configuration initiale

#### Configuration des serveurs
Éditer le fichier `src/main/config/servers.txt` :
```bash
# Serveurs locaux (priorité haute)
192.168.100.27
192.168.1.27
192.168.2.27

# Serveur distant (fallback)
https://edara.ligne-digitale.com
```

#### Personnalisation des logos (optionnel)
Remplacer les fichiers dans `src/renderer/assets/images/` :
- `logo-edara-claire.png` : Logo pour fond sombre
- `logo-edara-noire.png` : Logo pour fond clair
- `edara_illustration.svg` : Illustration vectorielle

## ✅ Vérification de l'installation

### Test de base
```bash
npm start
```

### Tests unitaires
```bash
# Test découverte des serveurs
node tests/unit/server-discovery.test.js

# Test authentification
node tests/unit/odoo-auth.test.js

# Test flux complet
node tests/integration/app-flow.test.js
```

## 🏗️ Build pour production

### Build local
```bash
npm run build
```

### Build pour différentes plateformes
```bash
# macOS
npm run build:mac

# Windows
npm run build:win

# Linux
npm run build:linux
```

## 🔧 Configuration avancée

### Variables d'environnement
Créer un fichier `.env` à la racine :
```env
# Timeout pour les tests de serveurs (ms)
SERVER_TEST_TIMEOUT=5000

# Niveau de log (debug, info, warn, error)
LOG_LEVEL=info

# Mode développement
NODE_ENV=development
```

### Configuration Electron Builder
Le fichier `package.json` contient la configuration de build :
```json
{
  "build": {
    "appId": "com.edara.electron-app",
    "productName": "Edara Workspace",
    "directories": {
      "output": "dist"
    },
    "files": [
      "src/**/*",
      "node_modules/**/*",
      "package.json"
    ]
  }
}
```

## 🐛 Résolution des problèmes

### Erreurs courantes

#### 1. Erreur de permissions (macOS/Linux)
```bash
sudo chown -R $(whoami) ~/.npm
```

#### 2. Erreur de certificats SSL
```bash
npm config set strict-ssl false
```

#### 3. Problème de proxy d'entreprise
```bash
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080
```

#### 4. Erreur "Cannot find module"
```bash
rm -rf node_modules package-lock.json
npm install
```

### Logs de débogage
Activer les logs détaillés :
```bash
DEBUG=* npm start
```

## 🔄 Mise à jour

### Mise à jour des dépendances
```bash
npm update
```

### Mise à jour de sécurité
```bash
npm audit fix
```

## 📱 Installation sur différents OS

### macOS
```bash
# Installation via Homebrew (optionnel)
brew install node npm git

# Installation du projet
git clone <repository-url>
cd edara-electron-app
npm install
npm start
```

### Windows
```bash
# Utiliser PowerShell ou Command Prompt
git clone <repository-url>
cd edara-electron-app
npm install
npm start
```

### Linux (Ubuntu/Debian)
```bash
# Installation des prérequis
sudo apt update
sudo apt install nodejs npm git

# Installation du projet
git clone <repository-url>
cd edara-electron-app
npm install
npm start
```

## 🎯 Validation de l'installation

### Checklist finale
- [ ] Node.js et npm installés
- [ ] Projet cloné et dépendances installées
- [ ] Configuration des serveurs effectuée
- [ ] Application démarre sans erreur
- [ ] Tests unitaires passent
- [ ] Interface splash s'affiche
- [ ] Découverte des serveurs fonctionne
- [ ] Authentification Odoo réussie

### Commandes de validation
```bash
# Vérifier la structure
ls -la src/

# Tester la découverte
node tests/unit/server-discovery.test.js

# Démarrer l'application
npm start
```

## 📞 Support

En cas de problème :
1. Vérifier les logs dans la console (F12)
2. Consulter la documentation dans `docs/`
3. Ouvrir une issue sur GitHub
4. Contacter l'équipe de développement

## 🔄 Désinstallation

```bash
# Supprimer le dossier du projet
rm -rf edara-electron-app

# Nettoyer le cache npm (optionnel)
npm cache clean --force
```
