/**
 * Gestionnaire de connexion côté renderer pour l'application Edara
 * Gère l'interface utilisateur et la communication avec le processus principal
 */

class LoginHandler {
  constructor() {
    this.isConnecting = false;
    this.availableServers = [];
    this.initializeEventListeners();
  }

  /**
   * Initialiser les écouteurs d'événements
   */
  initializeEventListeners() {
    // Attendre que le DOM soit chargé
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupUI());
    } else {
      this.setupUI();
    }
  }

  /**
   * Configurer l'interface utilisateur
   */
  setupUI() {
    try {
      // Récupérer les éléments du formulaire
      this.form = document.querySelector('.edara-login-form');
      this.emailInput = document.getElementById('email');
      this.passwordInput = document.getElementById('password');
      this.instanceSelect = document.getElementById('instance');
      this.loginButton = document.querySelector('.login-button');

      if (!this.form || !this.emailInput || !this.passwordInput || !this.instanceSelect || !this.loginButton) {
        throw new Error('Éléments du formulaire non trouvés');
      }

      // Ajouter les écouteurs d'événements
      this.form.addEventListener('submit', (e) => this.handleLogin(e));
      this.instanceSelect.addEventListener('change', () => this.handleInstanceChange());

      // Charger la liste des serveurs disponibles
      this.loadAvailableServers();

      window.electronAPI.logInfo('Interface de connexion initialisée');

    } catch (error) {
      window.electronAPI.logError('Erreur lors de l\'initialisation de l\'interface:', error);
      this.showError('Erreur d\'initialisation de l\'interface');
    }
  }

  /**
   * Charger la liste des serveurs disponibles
   */
  async loadAvailableServers() {
    try {
      const result = await window.electronAPI.getAvailableServers();

      if (result.success) {
        this.availableServers = result.servers;

        // NOUVEAU: Charger aussi le serveur préféré découvert
        await this.loadPreferredServer();

        this.updateServerStatus();
      } else {
        window.electronAPI.logError('Erreur lors du chargement des serveurs:', result.error);
      }

    } catch (error) {
      window.electronAPI.logError('Erreur lors du chargement des serveurs:', error);
    }
  }

  /**
   * NOUVEAU: Charger le serveur préféré découvert automatiquement
   */
  async loadPreferredServer() {
    try {
      const result = await window.electronAPI.getPreferredServer();

      if (result.success && result.preferredServer) {
        this.preferredServer = result.preferredServer;
        window.electronAPI.logInfo(`🎯 Serveur préféré chargé: ${this.preferredServer.url} (${this.preferredServer.type})`);
      } else {
        this.preferredServer = null;
        window.electronAPI.logInfo('Aucun serveur préféré découvert');
      }
    } catch (error) {
      window.electronAPI.logError('Erreur lors du chargement du serveur préféré:', error);
      this.preferredServer = null;
    }
  }

  /**
   * Mettre à jour le statut des serveurs dans l'interface
   */
  updateServerStatus() {
    const localServers = this.availableServers.filter(s => s.type === 'local' && s.available);
    const remoteServer = this.availableServers.find(s => s.type === 'remote');

    // Mettre à jour les options du select
    const localOption = this.instanceSelect.querySelector('option[value="local"]');
    const remoteOption = this.instanceSelect.querySelector('option[value="distance"]');

    if (localOption) {
      localOption.textContent = 'Connexion Local';
      localOption.disabled = localServers.length === 0;
    }

    if (remoteOption) {
      remoteOption.textContent = `Connexion Distance ${remoteServer?.available ? '✓' : '✗'}`;
      remoteOption.disabled = !remoteServer?.available;
    }

    // NOUVEAU: Sélectionner automatiquement selon le serveur préféré découvert
    if (this.preferredServer) {
      // Utiliser le serveur préféré découvert automatiquement
      if (this.preferredServer.type === 'local') {
        this.instanceSelect.value = 'local';
        window.electronAPI.logInfo(`🎯 Sélection automatique: Connexion Local (serveur découvert: ${this.preferredServer.url})`);
      } else {
        this.instanceSelect.value = 'distance';
        window.electronAPI.logInfo(`🎯 Sélection automatique: Connexion Distance (serveur découvert: ${this.preferredServer.url})`);
      }
    } else {
      // Fallback: Sélectionner automatiquement la meilleure option disponible
      if (localServers.length > 0) {
        this.instanceSelect.value = 'local';
        window.electronAPI.logInfo('🔄 Fallback: Sélection automatique Connexion Local');
      } else if (remoteServer?.available) {
        this.instanceSelect.value = 'distance';
        window.electronAPI.logInfo('🔄 Fallback: Sélection automatique Connexion Distance');
      }
    }
  }

  /**
   * Gérer le changement de type d'instance
   */
  handleInstanceChange() {
    const selectedInstance = this.instanceSelect.value;
    window.electronAPI.logInfo(`Type d'instance sélectionné: ${selectedInstance}`);
  }

  /**
   * Gérer la soumission du formulaire de connexion
   * @param {Event} event - Événement de soumission
   */
  async handleLogin(event) {
    event.preventDefault();

    if (this.isConnecting) {
      return;
    }

    try {
      // Valider les champs
      const email = this.emailInput.value.trim();
      const password = this.passwordInput.value;
      const instance = this.instanceSelect.value;

      if (!email || !password) {
        this.showError('Veuillez remplir tous les champs');
        return;
      }

      // Démarrer la connexion
      this.startConnecting();

      // Préparer les données de connexion
      const credentials = {
        email: email,
        password: password,
        instance: instance
      };

      window.electronAPI.logInfo(`Tentative de connexion pour ${email} (${instance})`);

      // Envoyer la demande d'authentification
      const authResult = await window.electronAPI.authenticateOdoo(credentials);

      if (authResult.success) {
        window.electronAPI.logInfo('Authentification réussie');

        // Afficher l'écran de chargement
        await window.electronAPI.showSplashScreen();

        // Charger l'interface Odoo
        await window.electronAPI.showOdooInterface(authResult.data);

        this.showSuccess('Connexion réussie !');

      } else {
        window.electronAPI.logError('Échec de l\'authentification:', authResult.error);
        this.showError(authResult.error || 'Échec de la connexion');
      }

    } catch (error) {
      window.electronAPI.logError('Erreur lors de la connexion:', error);
      this.showError('Erreur de connexion. Veuillez réessayer.');
    } finally {
      this.stopConnecting();
    }
  }

  /**
   * Démarrer l'état de connexion
   */
  startConnecting() {
    this.isConnecting = true;
    this.loginButton.disabled = true;
    this.loginButton.textContent = 'Connexion en cours...';
    this.loginButton.style.opacity = '0.7';

    // Ajouter une classe CSS pour l'animation de chargement
    this.loginButton.classList.add('connecting');
  }

  /**
   * Arrêter l'état de connexion
   */
  stopConnecting() {
    this.isConnecting = false;
    this.loginButton.disabled = false;
    this.loginButton.textContent = 'Se connecter';
    this.loginButton.style.opacity = '1';

    // Supprimer la classe CSS d'animation
    this.loginButton.classList.remove('connecting');
  }

  /**
   * Afficher un message d'erreur
   * @param {string} message - Message d'erreur
   */
  showError(message) {
    this.removeExistingMessages();

    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
      color: var(--error-color);
      background-color: rgba(231, 76, 60, 0.1);
      border: 1px solid var(--error-color);
      padding: 10px;
      border-radius: 4px;
      margin-top: 15px;
      font-size: 14px;
      text-align: center;
    `;

    this.form.appendChild(errorDiv);

    // Supprimer le message après 5 secondes
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv);
      }
    }, 5000);
  }

  /**
   * Afficher un message de succès
   * @param {string} message - Message de succès
   */
  showSuccess(message) {
    this.removeExistingMessages();

    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.textContent = message;
    successDiv.style.cssText = `
      color: var(--success-color);
      background-color: rgba(46, 204, 113, 0.1);
      border: 1px solid var(--success-color);
      padding: 10px;
      border-radius: 4px;
      margin-top: 15px;
      font-size: 14px;
      text-align: center;
    `;

    this.form.appendChild(successDiv);

    // Supprimer le message après 3 secondes
    setTimeout(() => {
      if (successDiv.parentNode) {
        successDiv.parentNode.removeChild(successDiv);
      }
    }, 3000);
  }

  /**
   * Supprimer les messages existants
   */
  removeExistingMessages() {
    const existingMessages = this.form.querySelectorAll('.error-message, .success-message');
    existingMessages.forEach(msg => {
      if (msg.parentNode) {
        msg.parentNode.removeChild(msg);
      }
    });
  }

  /**
   * Tester la connexion vers un serveur spécifique
   * @param {string} serverUrl - URL du serveur à tester
   * @returns {Promise<boolean>} - true si la connexion est réussie
   */
  async testServerConnection(serverUrl) {
    try {
      const result = await window.electronAPI.testConnection(serverUrl);
      return result.success && result.available;
    } catch (error) {
      window.electronAPI.logError(`Erreur lors du test de connexion vers ${serverUrl}:`, error);
      return false;
    }
  }

  /**
   * Rafraîchir la liste des serveurs disponibles
   */
  async refreshServerList() {
    window.electronAPI.logInfo('Rafraîchissement de la liste des serveurs...');
    await this.loadAvailableServers();
  }
}

// Initialiser le gestionnaire de connexion quand le script est chargé
const loginHandler = new LoginHandler();

// Exposer globalement pour le débogage
window.loginHandler = loginHandler;
