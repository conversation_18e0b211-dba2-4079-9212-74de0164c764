<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edara Workspace</title>
    <style>
        /* Variables CSS pour le thème sombre (par défaut) */
        :root {
            --background-color: #181818;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
            --progress-bar-background: #333333;
            --progress-bar-fill: #FFFFFF;
            --logo-shadow: rgba(0, 0, 0, 0.3);
        }

        /* Variables CSS pour le thème clair */
        @media (prefers-color-scheme: light) {
            :root {
                --background-color: #F5F5F5;
                --text-color: #212529;
                --secondary-text-color: #6C757D;
                --progress-bar-background: #DDDDDD;
                --progress-bar-fill: #212529;
                --logo-shadow: rgba(0, 0, 0, 0.1);
            }
        }

        /* Classes pour le basculement manuel du thème */
        .light-theme {
            --background-color: #F5F5F5;
            --text-color: #212529;
            --secondary-text-color: #6C757D;
            --progress-bar-background: #DDDDDD;
            --progress-bar-fill: #212529;
            --logo-shadow: rgba(0, 0, 0, 0.1);
        }

        .dark-theme {
            --background-color: #181818;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
            --progress-bar-background: #333333;
            --progress-bar-fill: #FFFFFF;
            --logo-shadow: rgba(0, 0, 0, 0.3);
        }

        /* Styles pour la page de splash */
        body {
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .splash-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            max-width: 480px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .logo-container {
            margin-bottom: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            transform: scale(0.6);
            opacity: 0.1;
            transition: all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            filter: drop-shadow(0 2px 8px var(--logo-shadow));
        }

        .app-title {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 40px;
            color: var(--text-color);
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.5s ease, transform 0.5s ease, color 0.3s ease;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .progress-container {
            width: 100%;
            max-width: 280px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .progress-bar {
            width: 100%;
            height: 3px;
            background-color: var(--progress-bar-background);
            border-radius: 2px;
            overflow: hidden;
            opacity: 0;
            transform: scaleX(0.9);
            transition: opacity 0.5s ease, transform 0.5s ease, background-color 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .progress-fill {
            height: 100%;
            width: 0;
            background: linear-gradient(90deg, var(--progress-bar-fill), rgba(255, 255, 255, 0.8));
            border-radius: 2px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
            border-radius: 0 2px 2px 0;
        }

        .loading-text {
            font-size: 14px;
            font-weight: 400;
            color: var(--secondary-text-color);
            opacity: 0;
            transform: translateY(5px);
            transition: opacity 0.5s ease, transform 0.5s ease, color 0.3s ease;
            text-align: center;
            line-height: 1.4;
            letter-spacing: 0.2px;
            min-height: 20px;
        }

        /* Animation de pulsation pour le logo */
        @keyframes pulse {
            0% {
                transform: scale(1);
                filter: drop-shadow(0 4px 8px var(--logo-shadow));
            }
            50% {
                transform: scale(1.08);
                filter: drop-shadow(0 8px 16px var(--logo-shadow));
            }
            100% {
                transform: scale(1);
                filter: drop-shadow(0 4px 8px var(--logo-shadow));
            }
        }

        /* Animation finale */
        @keyframes final-animation {
            0% { transform: scale(1); opacity: 1; }
            20% { transform: scale(1.15); opacity: 1; }
            40% { transform: scale(0.95); opacity: 1; }
            60% { transform: scale(1.05); opacity: 1; }
            80% { transform: scale(0.98); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        .logo.pulse {
            animation: pulse 2s infinite ease-in-out;
        }

        .logo.final-animation {
            animation: final-animation 1s ease-in-out forwards;
        }

        /* Animation de brillance pour la barre de progression */
        .progress-bar {
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 40%,
                rgba(255, 255, 255, 0.4) 50%,
                rgba(255, 255, 255, 0.2) 60%,
                transparent 100%);
            animation: progress-shine 2s infinite;
            transform: translateX(-100%);
            border-radius: 2px;
            z-index: 1;
        }

        @keyframes progress-shine {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Responsive design improvements */
        @media (max-width: 480px) {
            .splash-container {
                padding: 20px 16px;
                max-width: 100%;
            }

            .app-title {
                font-size: 20px;
                margin-bottom: 32px;
            }

            .progress-container {
                max-width: 240px;
            }

            .logo {
                width: 70px;
                height: 70px;
            }
        }

        @media (max-width: 320px) {
            .progress-container {
                max-width: 200px;
            }

            .app-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <!-- Logo -->
        <div class="logo-container">
            <img src="../assets/images/logo-edara-claire.png" alt="Edara Logo" id="logo" class="logo">
        </div>

        <!-- Titre -->
        <div class="app-title" id="appTitle">Edara Workspace</div>

        <!-- Conteneur de progression -->
        <div class="progress-container">
            <!-- Barre de progression -->
            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <!-- Texte de chargement -->
            <div class="loading-text" id="loadingText">Initialisation en cours...</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Éléments DOM
            const logo = document.getElementById('logo');
            const appTitle = document.getElementById('appTitle');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const loadingText = document.getElementById('loadingText');

            // Messages de chargement
            const loadingMessages = [
                'Initialisation en cours...',
                'Chargement des composants...',
                'Préparation de l\'interface...',
                'Finalisation...'
            ];
            let currentMessageIndex = 0;

            // Fonction pour détecter le thème du système
            function detectColorScheme() {
                const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

                // Mettre à jour le logo en fonction du thème
                if (logo) {
                    logo.src = isDarkMode
                        ? '../assets/images/logo-edara-claire.png'
                        : '../assets/images/logo-edara-noire.png';
                }
            }

            // Détecter le thème initial
            detectColorScheme();

            // Écouter les changements de thème
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
                detectColorScheme();
            });

            // Fonction pour mettre à jour le message de chargement
            function updateLoadingMessage(index) {
                if (loadingText && loadingMessages[index]) {
                    loadingText.style.opacity = '0';
                    setTimeout(() => {
                        loadingText.textContent = loadingMessages[index];
                        loadingText.style.opacity = '1';
                    }, 150);
                }
            }

            // Séquence d'animation améliorée
            setTimeout(() => {
                // Afficher le titre avec un délai
                setTimeout(() => {
                    appTitle.style.opacity = '1';
                    appTitle.style.transform = 'translateY(0)';

                    // Afficher la barre de progression et le texte avec un délai
                    setTimeout(() => {
                        progressBar.style.opacity = '1';
                        progressBar.style.transform = 'scaleX(1)';

                        // Afficher le texte de chargement
                        setTimeout(() => {
                            loadingText.style.opacity = '1';
                            loadingText.style.transform = 'translateY(0)';

                            // Initialiser la progression à 5%
                            updateProgressBar(5);

                            // Simuler une progression
                            simulateProgress();
                        }, 150);
                    }, 200);
                }, 300);
            }, 300);

            // Fonction pour mettre à jour la barre de progression
            function updateProgressBar(percent) {
                // Mettre à jour la largeur de la barre de progression
                progressFill.style.width = `${percent}%`;

                // Faire grandir progressivement le logo en fonction de la progression
                // Calculer la taille et l'opacité en fonction de la progression
                const scale = 0.6 + (percent / 100) * 0.4; // De 0.6 à 1

                // Assurer que l'opacité atteint exactement 1 à la fin (100%)
                let opacity;
                if (percent >= 100) {
                    opacity = 1;
                } else {
                    opacity = 0.1 + (percent / 100) * 0.9; // De 0.1 à 1
                }

                // Appliquer les styles directement
                logo.style.transform = `scale(${scale})`;
                logo.style.opacity = opacity;
            }

            // Fonction pour simuler une progression
            function simulateProgress() {
                let progress = 5;
                const totalDuration = 8000; // 8 secondes au total
                const totalSteps = 95; // De 5% à 100% = 95 étapes
                const interval = totalDuration / totalSteps; // Environ 84ms par étape

                // Points de changement de message (en pourcentage)
                const messagePoints = [25, 50, 75];

                // Mettre à jour la progression initiale
                updateProgressBar(progress);

                // Démarrer l'intervalle de progression
                const progressInterval = setInterval(() => {
                    progress += 1; // Incrément de 1% à chaque fois
                    updateProgressBar(progress);

                    // Changer le message aux points définis
                    if (messagePoints.includes(progress) && currentMessageIndex < loadingMessages.length - 1) {
                        currentMessageIndex++;
                        updateLoadingMessage(currentMessageIndex);
                    }

                    // Message final à 90%
                    if (progress === 90 && currentMessageIndex < loadingMessages.length - 1) {
                        currentMessageIndex = loadingMessages.length - 1;
                        updateLoadingMessage(currentMessageIndex);
                    }

                    // Quand on atteint 100%, terminer la progression
                    if (progress >= 100) {
                        clearInterval(progressInterval);

                        // S'assurer que la barre est à 100%
                        updateProgressBar(100);

                        // Lancer l'animation finale du logo
                        setTimeout(() => {
                            // Retirer le style de transformation mais garder l'opacité à 1
                            logo.style.transform = '';
                            logo.style.opacity = '1';

                            // Ajouter la classe pour l'animation finale
                            logo.classList.add('final-animation');

                            // Après l'animation finale, transition vers Odoo
                            setTimeout(() => {
                                logo.classList.remove('final-animation');
                                logo.classList.add('pulse');

                                // Masquer le texte de chargement
                                loadingText.style.opacity = '0';

                                // Transition automatique vers l'interface Odoo après 1 seconde
                                setTimeout(() => {
                                    // Simuler la transition vers Odoo
                                    if (window.electronAPI && window.electronAPI.transitionToOdoo) {
                                        window.electronAPI.transitionToOdoo();
                                    } else {
                                        // Fallback: masquer le splash et afficher un message
                                        document.body.style.opacity = '0';
                                        console.log('Transition vers l\'interface Odoo...');
                                    }
                                }, 1000);
                            }, 1000);
                        }, 300);
                    }
                }, interval);
            }
        });
    </script>
</body>
</html>
