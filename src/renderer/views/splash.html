<!DOCTYPE html>
<html lang="fr-FR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edara Workspace - Démarrage</title>
    <!-- Ajout des polices Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@700&family=Proxima+Nova:wght@400&display=swap" rel="stylesheet">
    <style>
        /* Variables CSS pour le thème - Harmonisées avec splash-final.html */
        :root {
            /* Color variables - Dark theme (default) */
            --color-dark-bg: #181818;
            --color-darker-bg: #1F1E1E;
            --color-button-primary: #124559;
            --color-button-secondary: #3a3b40;
            --color-text: #F8F9FA;
            --color-divider: rgba(255, 255, 255, 0.1);
            --progress-bar-background: #333333;
            --progress-bar-fill: #FFFFFF;
            --color-secondary-text: #DEE2E6;
            --background-color: #181818;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
        }

        /* Support for light theme */
        @media (prefers-color-scheme: light) {
            :root {
                --color-dark-bg: #F5F5F5;
                --color-darker-bg: #FFFFFF;
                --color-button-primary: #124559;
                --color-button-secondary: #6C757D;
                --color-text: #212529;
                --color-divider: rgba(0, 0, 0, 0.1);
                --progress-bar-background: #DDDDDD;
                --progress-bar-fill: #212529;
                --color-secondary-text: #6C757D;
                --background-color: #F5F5F5;
                --text-color: #212529;
                --secondary-text-color: #6C757D;
            }
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Proxima Nova', sans-serif; /* Default font */
            background-color: var(--color-dark-bg); /* Use theme variable */
            color: var(--color-text);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh; /* Ensure container takes full height */
            width: 100%;
            height: 100vh;
            overflow: hidden;
            -webkit-app-region: no-drag; /* Ne pas permettre de déplacer la fenêtre en cliquant n'importe où */
        }

        /* Les éléments interactifs ne doivent pas être draggable */
        button, input, select, a, .btn {
            -webkit-app-region: no-drag;
        }

        .container {
            display: flex;
            width: 100%; /* Prend toute la largeur */
            height: 100vh; /* Prend toute la hauteur */
            background-color: var(--color-darker-bg);
            overflow: hidden; /* Important for absolute positioning */
            position: relative; /* Pour le positionnement absolu de la ligne */
            border-radius: 0; /* Pas de coins arrondis */
        }

        .left-panel {
            flex-basis: 30%; /* Adjust ratio */
            background-color: var(--color-darker-bg);
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 30px;
            box-sizing: border-box;
            position: relative; /* Pour contenir les logos positionnés absolument */
        }

        /* Règle .left-panel img désactivée pour les logos adaptatifs */

        /* Logos adaptatifs selon le mode sombre/clair */
        .logo-adaptive {
            width: 130px !important;        /* Taille fixe en pixels */
            height: auto !important;
            max-width: none !important;     /* Supprimer la limite max-width */
            transition: opacity 0.3s ease;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Par défaut (mode sombre) : logo clair visible */
        .logo-light {
            display: block;
        }

        .logo-dark {
            display: none;
        }

        /* Mode clair : logo sombre visible */
        @media (prefers-color-scheme: light) {
            .logo-light {
                display: none !important;
            }

            .logo-dark {
                display: block !important;
            }
        }

        .right-panel {
            flex-basis: 70%; /* Adjust ratio */
            background-color: var(--color-dark-bg);
            color: var(--color-text);
            padding: 5% 7%; /* Use percentage padding for responsiveness */
            box-sizing: border-box;
            position: relative; /* Needed for absolute positioning of faded logo */
            display: flex;
            flex-direction: column;
        }

        .right-panel h1 {
            font-family: 'Open Sans', sans-serif;
            font-weight: 700;
            font-size: clamp(25px, 3.5vw, 30px); /* Adjusted clamp */
            line-height: 1.0;
            margin: 0 0 25px 0;
        }

        .description {
            font-size: 16px;
            line-height: 1.0;
            margin-bottom: 30px;
        }

        /* Barre de progression - Design professionnel amélioré */
        .progress-container {
            width: 100%;
            max-width: 280px;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .progress-bar {
            width: 100%;
            height: 3px;
            background-color: var(--progress-bar-background);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 10px;
            position: relative;
            opacity: 1;
            transform: scaleX(1);
            transition: opacity 0.5s ease, transform 0.5s ease, background-color 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .progress-bar-fill {
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, var(--progress-bar-fill), rgba(255, 255, 255, 0.8));
            border-radius: 2px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s ease;
            position: relative;
        }

        .progress-bar-fill::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
            border-radius: 0 2px 2px 0;
        }

        /* Animation de brillance pour la barre de progression */
        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 40%,
                rgba(255, 255, 255, 0.4) 50%,
                rgba(255, 255, 255, 0.2) 60%,
                transparent 100%);
            animation: progress-shine 2s infinite;
            transform: translateX(-100%);
            border-radius: 2px;
            z-index: 1;
        }

        @keyframes progress-shine {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Texte de chargement - Design amélioré */
        .loading-text {
            font-size: 14px;
            font-weight: 400;
            color: var(--color-secondary-text);
            margin: 0;
            min-height: 20px;
            text-align: center;
            line-height: 1.4;
            letter-spacing: 0.2px;
            transition: opacity 0.3s ease, color 0.3s ease;
            opacity: 1;
        }

        /* Responsive design improvements */
        @media (max-width: 768px) {
            .progress-container {
                max-width: 240px;
                margin: 15px auto;
            }

            .right-panel {
                padding: 4% 5%;
            }
        }

        @media (max-width: 480px) {
            .progress-container {
                max-width: 200px;
            }

            .right-panel h1 {
                font-size: clamp(20px, 4vw, 25px);
            }

            .loading-text {
                font-size: 13px;
            }
        }

        /* Footer area and buttons */
        .footer-area {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px 7%;
            border-top: 1px solid var(--color-divider);
        }

        .button-group {
            display: flex;
            justify-content: flex-end; /* Align buttons to the right */
            gap: 15px;
        }

        .btn {
            color: var(--color-text);
            border: none;
            padding: 10px 25px;
            border-radius: 4px;
            font-family: 'Proxima Nova', sans-serif;
            font-size: 15px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-weight: 400;
            letter-spacing: 0.2px;
        }

        .btn-retry {
            background-color: var(--color-button-primary);
            display: none; /* Caché par défaut */
            color: #ffffff; /* Toujours blanc pour contraste */
        }

        .btn-cancel {
            background-color: var(--color-button-secondary);
        }

        .btn:hover {
            opacity: 0.9;
        }

        /* Logo estompé supprimé */

        /* État d'erreur */
        .error-state h1 {
            color: #ff6b6b;
        }

        /* Nous n'utilisons plus l'animation des points car ils sont inclus dans le texte */
    </style>
</head>
<body>
    <!-- Pas de barre de titre personnalisée, utilisation de la barre native de macOS -->

    <div class="container" id="mainContainer">
        <div class="left-panel">
            <!-- Option 1: Illustration SVG (décommentez pour utiliser) -->
            <!-- <img src="../assets/images/edara_illustration.svg" alt="Illustration Edara" class="logo-adaptive"> -->

            <!-- Option 2: Logos adaptatifs selon le mode sombre/clair -->
            <img src="../assets/images/logo-edara-claire.png" alt="Logo Edara" class="logo-adaptive logo-light">
            <img src="../assets/images/logo-edara-noire.png" alt="Logo Edara" class="logo-adaptive logo-dark">
        </div>
        <div class="right-panel">
            <h1 id="mainTitle">Connexion en cours</h1>

            <div class="progress-container">
                <div class="progress-bar" id="progressBar">
                    <div class="progress-bar-fill" id="progressFill"></div>
                </div>
                <p class="loading-text" id="loadingText">Initialisation de l'application...</p>
            </div>

            <!-- Logo estompé supprimé -->

            <div class="footer-area">
                <div class="button-group">
                    <button class="btn btn-retry" id="retryButton">Réessayer</button>
                    <button class="btn btn-cancel" id="cancelButton">Annuler</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Éléments DOM - Harmonisés avec splash-final.html
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const loadingText = document.getElementById('loadingText');
        const retryButton = document.getElementById('retryButton');
        const cancelButton = document.getElementById('cancelButton');
        const mainContainer = document.getElementById('mainContainer');

        // Messages de chargement
        const loadingMessages = [
            'Initialisation de l\'application',
            'Chargement des paramètres système',
            'Vérification de la connexion au serveur'
        ];

        // État actuel
        let currentMessageIndex = 0;
        let progress = 0;
        let isConnecting = true;
        let checkingServer = false;

        // Fonction pour mettre à jour la barre de progression - Harmonisée avec splash-final.html
        function updateProgressBar(value) {
            if (progressFill) {
                progressFill.style.width = `${value}%`;
            }
        }

        // Fonction pour mettre à jour le message de chargement
        function updateLoadingMessage(index) {
            // Ajouter les points de suspension directement dans le texte
            loadingText.textContent = loadingMessages[index] + '...';
            // Nous n'utilisons plus l'animation des points car ils sont inclus dans le texte
        }

        // Fonction pour vérifier la connexion au serveur
        async function checkServerConnection() {
            if (checkingServer) return;
            checkingServer = true;

            try {
                // Vérifier la connexion au serveur via l'API Electron
                if (window.electronAPI && typeof window.electronAPI.getServerInfo === 'function') {
                    const result = await window.electronAPI.getServerInfo();
                    if (result.success) {
                        // Passer les informations du serveur à la fonction connectionSuccessful
                        connectionSuccessful({
                            isLocal: result.isLocal,
                            serverUrl: result.serverUrl,
                            fallback: result.fallback
                        });
                    } else {
                        connectionFailed();
                        // Pas de reconnexion automatique - l'utilisateur doit cliquer sur "Réessayer"
                    }
                } else {
                    // Simuler une vérification de connexion si l'API n'est pas disponible
                    console.error('API electronAPI.getServerInfo non disponible');
                    setTimeout(() => {
                        // Sans information sur le serveur
                        connectionSuccessful({});
                    }, 1000);
                }
            } catch (error) {
                // Si la connexion échoue
                connectionFailed();
                // Pas de reconnexion automatique - l'utilisateur doit cliquer sur "Réessayer"
            } finally {
                checkingServer = false;
            }
        }

        // Fonction appelée lorsque la connexion est établie
        function connectionSuccessful(serverInfo) {
            // Déterminer si le serveur est local ou distant
            const isLocalServer = serverInfo && serverInfo.isLocal;
            const serverUrl = serverInfo && serverInfo.serverUrl ? serverInfo.serverUrl : '';

            // Mettre à jour le message avec l'information du serveur
            if (isLocalServer) {
                loadingText.innerHTML = 'Connexion établie avec succès.<br><span style="font-size: 12px; color: var(--color-secondary-text);">Utilisation du serveur local</span>';
            } else if (serverUrl.includes('localhost')) {
                loadingText.innerHTML = 'Connexion établie avec succès.<br><span style="font-size: 12px; color: var(--color-secondary-text);">Utilisation du serveur local</span>';
            } else {
                loadingText.innerHTML = 'Connexion établie avec succès.<br><span style="font-size: 12px; color: var(--color-secondary-text);">Utilisation du serveur distant</span>';
            }

            // Mettre à jour la barre de progression
            updateProgressBar(100);

            // Envoyer immédiatement le message à l'application principale pour indiquer que la connexion est réussie
            console.log('Connexion établie, fermeture immédiate de la fenêtre de démarrage');

            if (window.electronAPI && typeof window.electronAPI.connectionSuccessful === 'function') {
                window.electronAPI.connectionSuccessful(serverUrl);
            } else {
                console.error('API electronAPI.connectionSuccessful non disponible');
            }
        }

        // Fonction appelée lorsque la connexion échoue
        function connectionFailed() {
            // Mettre à jour l'interface pour afficher l'erreur
            mainContainer.classList.add('error-state');

            // Changer le titre principal
            document.getElementById('mainTitle').textContent = 'Problème de connexion!';

            // Afficher le message d'erreur sur deux lignes
            loadingText.innerHTML = 'Impossible de rejoindre le serveur.<br>Veuillez réessayer.';
            loadingText.style.whiteSpace = 'pre-line'; // Permet les sauts de ligne

            // Afficher le bouton Réessayer et le mettre en évidence
            retryButton.style.display = 'block';
            retryButton.style.backgroundColor = 'var(--color-button-primary)';
            retryButton.style.fontWeight = 'bold';
            loadingText.style.color = 'var(--color-secondary-text)'; // Couleur du texte d'erreur

            // Arrêter l'animation de la barre de progression
            isConnecting = false;

            // Mettre à jour la barre de progression pour montrer l'échec
            updateProgressBar(30); // Réduire la barre de progression pour indiquer l'échec
        }

        // Fonction pour redémarrer le processus de connexion
        function retryConnection() {
            // Réinitialiser l'interface
            mainContainer.classList.remove('error-state');
            retryButton.style.display = 'none';

            // Réinitialiser le titre principal
            document.getElementById('mainTitle').textContent = 'Connexion en cours';

            // Réinitialiser les variables
            currentMessageIndex = 2; // Commencer directement à la vérification de connexion
            progress = 40;
            isConnecting = true;

            // Réinitialiser la couleur du texte
            loadingText.style.color = 'var(--color-secondary-text)';

            // Mettre à jour l'interface pour indiquer une nouvelle tentative
            loadingText.textContent = 'Vérification de la connexion au serveur...';
            updateProgressBar(progress);

            // Attendre un court instant puis vérifier directement la connexion
            setTimeout(() => {
                // Vérifier directement la connexion au serveur
                checkServerConnection();
            }, 1000);
        }

        // Fonction pour quitter l'application
        function cancelApplication() {
            if (window.electronAPI && typeof window.electronAPI.sendAuthData === 'function') {
                window.electronAPI.sendAuthData({ action: 'quit-application' });
            } else {
                console.error('API electronAPI.sendAuthData non disponible');
            }
        }

        // Fonction principale pour démarrer le processus de connexion
        function startConnectionProcess() {
            // Durée totale: 3000ms (3 secondes)
            const totalDuration = 3000; // en millisecondes
            const totalSteps = 100; // Nombre total d'étapes pour la barre de progression
            const intervalTime = totalDuration / totalSteps; // Temps entre chaque mise à jour

            // Points de changement de message (en pourcentage)
            const firstMessageChange = 33; // À 33% de la progression
            const secondMessageChange = 66; // À 66% de la progression

            // Mettre à jour la barre de progression progressivement
            const progressInterval = setInterval(() => {
                if (!isConnecting) {
                    clearInterval(progressInterval);
                    return;
                }

                progress += 1;
                updateProgressBar(progress);

                // Changer de message aux pourcentages définis
                if (progress === firstMessageChange && currentMessageIndex < 1) {
                    currentMessageIndex = 1;
                    updateLoadingMessage(currentMessageIndex);
                } else if (progress === secondMessageChange && currentMessageIndex < 2) {
                    currentMessageIndex = 2;
                    updateLoadingMessage(currentMessageIndex);
                }

                // Vérifier la connexion au serveur à la fin de la progression
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    checkServerConnection();
                }
            }, intervalTime);
        }

        // Ajouter un écouteur d'événement pour le bouton Réessayer
        retryButton.addEventListener('click', retryConnection);

        // Ajouter un écouteur d'événement pour le bouton Annuler
        cancelButton.addEventListener('click', cancelApplication);

        // Fonction pour adapter les logos selon le mode système
        function adaptLogos() {
            const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;

            // Logos principaux seulement (logo estompé supprimé)
            const logoLight = document.querySelector('.logo-light');
            const logoDark = document.querySelector('.logo-dark');

            if (isDarkMode) {
                // Mode sombre : afficher logo clair
                if (logoLight) logoLight.style.display = 'block';
                if (logoDark) logoDark.style.display = 'none';
            } else {
                // Mode clair : afficher logo sombre
                if (logoLight) logoLight.style.display = 'none';
                if (logoDark) logoDark.style.display = 'block';
            }
        }

        // Démarrer le processus au chargement de la page
        document.addEventListener('DOMContentLoaded', () => {
            // Adapter les logos au mode système
            adaptLogos();

            // Écouter les changements de mode
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', adaptLogos);

            // Démarrer le processus de connexion
            startConnectionProcess();
        });

        // Exposer la fonction de redémarrage pour l'API Electron
        if (window.electronAPI) {
            window.electronAPI.onRetryConnection = retryConnection;
        }
    </script>
</body>
</html>
