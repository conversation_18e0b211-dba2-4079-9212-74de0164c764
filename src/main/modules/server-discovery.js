/**
 * Module de découverte et sélection automatique des serveurs Odoo
 * Gère la priorité locale/distante et la vérification de disponibilité
 */

const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const log = require('electron-log');

class ServerDiscovery {
  constructor() {
    this.configFile = 'src/main/config/servers.txt';
    this.localServers = [];
    this.remoteServer = null;
    this.testTimeout = 5000; // 5 secondes
    this.selectedServer = null;
    this.serverStatus = new Map();
  }

  /**
   * Charger la configuration des serveurs depuis servers.txt
   * @returns {Promise<void>}
   */
  async loadServerConfig() {
    try {
      log.info('📋 Chargement de la configuration des serveurs...');

      const configPath = path.join(process.cwd(), this.configFile);
      const configContent = await fs.readFile(configPath, 'utf8');

      const lines = configContent.split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('#'));

      // Séparer les serveurs locaux et distants
      this.localServers = [];
      this.remoteServer = null;

      for (const line of lines) {
        if (line.startsWith('http://') || line.startsWith('https://')) {
          // URL complète (serveur distant)
          this.remoteServer = line;
          log.debug(`Serveur distant configuré: ${line}`);
        } else if (line.match(/^\d+\.\d+\.\d+\.\d+$/)) {
          // Adresse IP (serveur local)
          const localUrl = `http://${line}:8069`;
          this.localServers.push(localUrl);
          log.debug(`Serveur local configuré: ${localUrl}`);
        } else {
          log.warn(`Ligne ignorée dans servers.txt: ${line}`);
        }
      }

      log.info(`✅ Configuration chargée: ${this.localServers.length} serveurs locaux, ${this.remoteServer ? '1' : '0'} serveur distant`);
      log.debug('Serveurs locaux:', this.localServers);
      log.debug('Serveur distant:', this.remoteServer);

    } catch (error) {
      log.error('❌ Erreur lors du chargement de la configuration:', error);
      throw new Error(`Impossible de charger la configuration: ${error.message}`);
    }
  }

  /**
   * Tester la disponibilité d'un serveur
   * @param {string} serverUrl - URL du serveur à tester
   * @returns {Promise<Object>} - Résultat du test
   */
  async testServerAvailability(serverUrl) {
    try {
      log.debug(`🔍 Test de connexion vers: ${serverUrl}`);

      const startTime = Date.now();
      const response = await axios.get(`${serverUrl}/web/database/selector`, {
        timeout: this.testTimeout,
        validateStatus: (status) => status < 500 // Accepter les redirections
      });

      const responseTime = Date.now() - startTime;

      const result = {
        url: serverUrl,
        available: true,
        responseTime: responseTime,
        status: response.status,
        type: this.isLocalServer(serverUrl) ? 'local' : 'distant'
      };

      log.debug(`✅ Serveur ${result.type} disponible: ${serverUrl} (${responseTime}ms)`);
      return result;

    } catch (error) {
      const result = {
        url: serverUrl,
        available: false,
        error: error.message,
        type: this.isLocalServer(serverUrl) ? 'local' : 'distant'
      };

      log.debug(`❌ Serveur ${result.type} indisponible: ${serverUrl} - ${error.message}`);
      return result;
    }
  }

  /**
   * Vérifier si une URL correspond à un serveur local
   * @param {string} url - URL à vérifier
   * @returns {boolean}
   */
  isLocalServer(url) {
    return url.includes('192.168.') ||
           url.includes('localhost') ||
           url.includes('127.0.0.1') ||
           url.includes('10.') ||
           url.includes('172.');
  }

  /**
   * Découvrir et sélectionner le meilleur serveur disponible
   * @param {Function} progressCallback - Callback pour les mises à jour de progression
   * @returns {Promise<Object>} - Serveur sélectionné
   */
  async discoverBestServer(progressCallback = null) {
    try {
      log.info('🔍 === DÉBUT DÉCOUVERTE SERVEURS ===');

      // Étape 1: Charger la configuration
      if (progressCallback) {
        progressCallback({
          step: 1,
          message: 'Chargement de la configuration...',
          servers: []
        });
      }

      await this.loadServerConfig();

      // Étape 2: Préparer la liste de tous les serveurs
      const allServers = [...this.localServers];
      if (this.remoteServer) {
        allServers.push(this.remoteServer);
      }

      if (allServers.length === 0) {
        throw new Error('Aucun serveur configuré dans servers.txt');
      }

      if (progressCallback) {
        progressCallback({
          step: 2,
          message: `Test de ${allServers.length} serveur(s)...`,
          servers: allServers.map(url => ({
            name: this.getServerDisplayName(url),
            url: url,
            status: 'checking',
            statusText: 'Vérification...'
          }))
        });
      }

      // Étape 3: Tester tous les serveurs en parallèle
      log.info(`🔍 Test de ${allServers.length} serveurs...`);
      const testPromises = allServers.map(url => this.testServerAvailability(url));
      const testResults = await Promise.all(testPromises);

      // Stocker les résultats
      this.serverStatus.clear();
      testResults.forEach(result => {
        this.serverStatus.set(result.url, result);
      });

      // Étape 4: Analyser les résultats et sélectionner le meilleur
      const availableServers = testResults.filter(result => result.available);

      if (progressCallback) {
        progressCallback({
          step: 3,
          message: 'Analyse des résultats...',
          servers: testResults.map(result => ({
            name: this.getServerDisplayName(result.url),
            url: result.url,
            status: result.available ? 'available' : 'unavailable',
            statusText: result.available ?
              `Disponible (${result.responseTime}ms)` :
              'Indisponible',
            responseTime: result.responseTime
          }))
        });
      }

      if (availableServers.length === 0) {
        log.error('❌ Aucun serveur disponible');
        throw new Error('Aucun serveur Odoo n\'est accessible actuellement');
      }

      // Étape 5: Sélectionner le meilleur serveur (priorité aux serveurs locaux)
      const localAvailable = availableServers.filter(s => s.type === 'local');
      const remoteAvailable = availableServers.filter(s => s.type === 'distant');

      let selectedServer;

      if (localAvailable.length > 0) {
        // Prioriser le serveur local le plus rapide
        selectedServer = localAvailable.sort((a, b) => a.responseTime - b.responseTime)[0];
        log.info(`✅ Serveur local sélectionné: ${selectedServer.url} (${selectedServer.responseTime}ms)`);
      } else if (remoteAvailable.length > 0) {
        // Utiliser le serveur distant
        selectedServer = remoteAvailable[0];
        log.info(`✅ Serveur distant sélectionné: ${selectedServer.url} (${selectedServer.responseTime}ms)`);
      }

      this.selectedServer = selectedServer;

      if (progressCallback) {
        progressCallback({
          step: 4,
          message: `Connexion ${selectedServer.type} établie`,
          selectedServer: selectedServer,
          servers: testResults.map(result => ({
            name: this.getServerDisplayName(result.url),
            url: result.url,
            status: result.available ? 'available' : 'unavailable',
            statusText: result.available ?
              `Disponible (${result.responseTime}ms)` :
              'Indisponible',
            selected: result.url === selectedServer.url
          }))
        });
      }

      log.info('✅ === DÉCOUVERTE SERVEURS TERMINÉE ===');

      return {
        success: true,
        server: selectedServer,
        allResults: testResults,
        totalTested: allServers.length,
        totalAvailable: availableServers.length
      };

    } catch (error) {
      log.error('❌ Erreur lors de la découverte des serveurs:', error);

      if (progressCallback) {
        progressCallback({
          step: 0,
          message: 'Erreur de connexion',
          error: error.message
        });
      }

      return {
        success: false,
        error: error.message,
        allResults: [],
        totalTested: 0,
        totalAvailable: 0
      };
    }
  }

  /**
   * Obtenir un nom d'affichage pour un serveur
   * @param {string} url - URL du serveur
   * @returns {string} - Nom d'affichage
   */
  getServerDisplayName(url) {
    if (this.isLocalServer(url)) {
      const match = url.match(/(\d+\.\d+\.\d+\.\d+)/);
      return match ? `Serveur local (${match[1]})` : 'Serveur local';
    } else {
      try {
        const urlObj = new URL(url);
        return `Serveur distant (${urlObj.hostname})`;
      } catch {
        return 'Serveur distant';
      }
    }
  }

  /**
   * Obtenir le serveur sélectionné
   * @returns {Object|null} - Serveur sélectionné
   */
  getSelectedServer() {
    return this.selectedServer;
  }

  /**
   * Obtenir le statut de tous les serveurs
   * @returns {Map} - Map des statuts des serveurs
   */
  getServerStatus() {
    return this.serverStatus;
  }

  /**
   * Réinitialiser la découverte
   */
  reset() {
    this.selectedServer = null;
    this.serverStatus.clear();
    log.info('🔄 Découverte des serveurs réinitialisée');
  }
}

module.exports = ServerDiscovery;
