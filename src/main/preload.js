/**
 * Script de préchargement pour l'application Electron Edara
 * Ce script s'exécute dans le contexte du renderer avec accès aux APIs Node.js
 * Il expose des APIs sécurisées au contexte web via contextBridge
 */

const { contextBridge, ipcRenderer } = require('electron');

// Fonction de log simple pour le preload
const log = {
  info: (message) => console.log(`[Preload] ${message}`),
  error: (message, error) => console.error(`[Preload] ${message}`, error),
  debug: (message) => console.log(`[Preload Debug] ${message}`)
};

// Exposer les APIs sécurisées au contexte web
contextBridge.exposeInMainWorld('electronAPI', {
  // Authentification Odoo
  authenticateOdoo: (credentials) => {
    log.info('Demande d\'authentification envoyée depuis le renderer');
    return ipcRenderer.invoke('authenticate-odoo', credentials);
  },

  // Gestion des fenêtres
  showSplashScreen: () => {
    log.info('Demande d\'affichage de l\'écran de chargement');
    return ipcRenderer.invoke('show-splash-screen');
  },

  hideSplashScreen: () => {
    log.info('Demande de masquage de l\'écran de chargement');
    return ipcRenderer.invoke('hide-splash-screen');
  },

  showOdooInterface: (authData) => {
    log.info('Demande d\'affichage de l\'interface Odoo');
    return ipcRenderer.invoke('show-odoo-interface', authData);
  },

  // NOUVEAU: Notification de déconnexion Odoo
  notifyLogout: () => {
    log.info('🚪 Notification de déconnexion Odoo détectée');
    return ipcRenderer.invoke('handle-odoo-logout');
  },

  // NOUVEAU: Découverte automatique des serveurs
  discoverServers: () => {
    log.info('🔍 Lancement de la découverte des serveurs');
    return ipcRenderer.invoke('discover-servers');
  },

  // NOUVEAU: Retry de la découverte des serveurs
  retryServerCheck: () => {
    log.info('🔄 Nouvelle tentative de découverte des serveurs');
    return ipcRenderer.invoke('retry-server-discovery');
  },

  // NOUVEAU: Écouter les mises à jour de progression du splash
  onSplashUpdate: (callback) => {
    ipcRenderer.on('splash-update', callback);
  },

  // NOUVEAU: Écouter les erreurs du splash
  onSplashError: (callback) => {
    ipcRenderer.on('splash-error', callback);
  },

  // NOUVEAU: Écouter le succès du splash
  onSplashSuccess: (callback) => {
    ipcRenderer.on('splash-success', callback);
  },

  // Test de connectivité (conservé pour compatibilité)
  testConnection: (serverUrl) => {
    log.info(`Test de connexion vers: ${serverUrl}`);
    return ipcRenderer.invoke('test-connection', serverUrl);
  },

  // Gestion des serveurs
  getAvailableServers: () => {
    log.info('Demande de la liste des serveurs disponibles');
    return ipcRenderer.invoke('get-available-servers');
  },

  // NOUVEAU: Obtenir le serveur préféré découvert
  getPreferredServer: () => {
    log.info('Demande du serveur préféré découvert');
    return ipcRenderer.invoke('get-preferred-server');
  },

  // Événements
  onConnectionStatusChange: (callback) => {
    ipcRenderer.on('connection-status-change', callback);
  },

  onOdooReady: (callback) => {
    ipcRenderer.on('odoo-ready', callback);
  },

  onAuthenticationResult: (callback) => {
    ipcRenderer.on('authentication-result', callback);
  },

  // Nettoyage des listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Logs
  logInfo: (message) => {
    log.info(`[Renderer] ${message}`);
  },

  logError: (message, error) => {
    log.error(`[Renderer] ${message}`, error);
  },

  logDebug: (message) => {
    log.debug(`[Renderer] ${message}`);
  },

  // Utilitaires
  getAppVersion: () => {
    return ipcRenderer.invoke('get-app-version');
  },

  closeApp: () => {
    return ipcRenderer.invoke('close-app');
  },

  minimizeApp: () => {
    return ipcRenderer.invoke('minimize-app');
  },

  maximizeApp: () => {
    return ipcRenderer.invoke('maximize-app');
  }
});

// Log de démarrage
log.info('Preload script chargé avec succès');
