/**
 * Point d'entrée principal de l'application Electron Edara
 * Gère l'initialisation de l'application et la création des fenêtres
 */

const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const log = require('electron-log');

// Importer les modules personnalisés
const ConnectionManager = require('./modules/connection-manager');
const OdooAuth = require('./modules/odoo-auth');
const WindowManager = require('./modules/window-manager');
const ServerDiscovery = require('./modules/server-discovery');

// Configuration de electron-log
log.transports.file.level = 'info';
log.transports.console.level = 'debug';
log.transports.console.format = '[{level}] {text}';

// Variables globales
let connectionManager;
let odooAuth;
let windowManager;
let serverDiscovery;
let splashWindow;

/**
 * Configurer les arguments Electron pour optimiser Odoo
 */
function setupElectronArgs() {
  // Arguments pour améliorer la compatibilité avec Odoo
  app.commandLine.appendSwitch('disable-web-security');
  app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
  app.commandLine.appendSwitch('disable-site-isolation-trials');
  app.commandLine.appendSwitch('disable-background-timer-throttling');
  app.commandLine.appendSwitch('disable-backgrounding-occluded-windows');
  app.commandLine.appendSwitch('disable-renderer-backgrounding');
  app.commandLine.appendSwitch('ignore-certificate-errors');
  app.commandLine.appendSwitch('ignore-ssl-errors');
  app.commandLine.appendSwitch('ignore-certificate-errors-spki-list');
  app.commandLine.appendSwitch('allow-running-insecure-content');
  app.commandLine.appendSwitch('disable-http-cache');

  log.info('Arguments Electron configurés pour Odoo');
}

/**
 * Initialisation de l'application
 */
async function initializeApp() {
  try {
    log.info('Initialisation de l\'application Edara...');

    // Configurer les arguments Electron
    setupElectronArgs();

    // Initialiser les gestionnaires
    connectionManager = new ConnectionManager();
    odooAuth = new OdooAuth();
    windowManager = new WindowManager();

    // Charger la configuration des serveurs
    await connectionManager.loadServerConfig();

    log.info('Application initialisée avec succès');
  } catch (error) {
    log.error('Erreur lors de l\'initialisation de l\'application:', error);

    // Afficher une boîte de dialogue d'erreur
    dialog.showErrorBox(
      'Erreur d\'initialisation',
      `Impossible d'initialiser l'application: ${error.message}`
    );

    app.quit();
  }
}

/**
 * Créer la fenêtre splash pour la découverte des serveurs
 */
async function createSplashWindow() {
  try {
    log.info('🚀 Création de la fenêtre splash...');

    splashWindow = new BrowserWindow({
      width: 800,
      height: 250,
      show: true, // Afficher immédiatement
      frame: false,
      alwaysOnTop: true,
      resizable: false,
      center: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: false
      }
    });

    // Charger le fichier splash
    await splashWindow.loadFile(path.join(__dirname, '../renderer/views/splash.html'));
    log.info('✅ Fichier splash chargé');

    // NOUVEAU: Démarrer la découverte immédiatement après le chargement
    log.info('✅ Fenêtre splash prête - démarrage immédiat de la découverte');

    // Démarrer la découverte immédiatement
    setTimeout(() => {
      startServerDiscovery();
    }, 1000); // 1 seconde pour laisser le temps au splash de s'afficher

    // Gérer la fermeture de la fenêtre splash
    splashWindow.on('closed', () => {
      splashWindow = null;
    });

    log.info('✅ Fenêtre splash créée');
  } catch (error) {
    log.error('❌ Erreur lors de la création de la fenêtre splash:', error);
    throw error;
  }
}

/**
 * Démarrer la découverte automatique des serveurs
 */
async function startServerDiscovery() {
  try {
    log.info('🔍 === DÉBUT DÉCOUVERTE AUTOMATIQUE DES SERVEURS ===');

    // Initialiser le module de découverte
    serverDiscovery = new ServerDiscovery();

    // Callback pour les mises à jour de progression
    const progressCallback = (data) => {
      if (splashWindow && !splashWindow.isDestroyed()) {
        splashWindow.webContents.send('splash-update', data);
      }
    };

    // Lancer la découverte
    const discoveryResult = await serverDiscovery.discoverBestServer(progressCallback);

    if (discoveryResult.success) {
      log.info('✅ Découverte réussie, transition vers l\'interface de connexion');

      // Notifier le succès au splash
      if (splashWindow && !splashWindow.isDestroyed()) {
        splashWindow.webContents.send('splash-success', {
          type: discoveryResult.server.type,
          server: discoveryResult.server
        });
      }

      // Attendre un peu pour l'animation puis créer la fenêtre principale
      setTimeout(async () => {
        try {
          log.info('🎯 Création de la fenêtre principale après découverte réussie');
          await createMainWindow();

          // Fermer la fenêtre splash
          if (splashWindow && !splashWindow.isDestroyed()) {
            splashWindow.close();
            log.info('✅ Fenêtre splash fermée');
          }
        } catch (error) {
          log.error('❌ Erreur lors de la création de la fenêtre principale:', error);

          // En cas d'erreur, fermer quand même le splash et créer une fenêtre basique
          if (splashWindow && !splashWindow.isDestroyed()) {
            splashWindow.close();
          }

          // Créer une fenêtre principale sans serveur préféré
          try {
            await createMainWindow();
          } catch (fallbackError) {
            log.error('❌ Erreur critique lors du fallback:', fallbackError);
          }
        }
      }, 1500); // Réduire le délai à 1.5 secondes

    } else {
      log.error('❌ Découverte échouée:', discoveryResult.error);

      // Notifier l'erreur au splash
      if (splashWindow && !splashWindow.isDestroyed()) {
        splashWindow.webContents.send('splash-error', {
          error: discoveryResult.error
        });
      }

      // NOUVEAU: Fallback automatique après 5 secondes même en cas d'erreur
      setTimeout(async () => {
        log.warn('⚠️ Fallback automatique - création de la fenêtre principale malgré l\'échec de découverte');

        try {
          await createMainWindow();

          // Fermer la fenêtre splash
          if (splashWindow && !splashWindow.isDestroyed()) {
            splashWindow.close();
            log.info('✅ Fenêtre splash fermée (fallback)');
          }
        } catch (error) {
          log.error('❌ Erreur critique lors du fallback:', error);
        }
      }, 5000); // 5 secondes de délai pour permettre à l'utilisateur de voir l'erreur
    }

  } catch (error) {
    log.error('❌ Erreur lors de la découverte des serveurs:', error);

    // Notifier l'erreur au splash
    if (splashWindow && !splashWindow.isDestroyed()) {
      splashWindow.webContents.send('splash-error', {
        error: error.message
      });
    }

    // NOUVEAU: Fallback automatique même en cas d'erreur critique
    setTimeout(async () => {
      log.warn('⚠️ Fallback critique - création de la fenêtre principale malgré l\'erreur');

      try {
        await createMainWindow();

        // Fermer la fenêtre splash
        if (splashWindow && !splashWindow.isDestroyed()) {
          splashWindow.close();
          log.info('✅ Fenêtre splash fermée (fallback critique)');
        }
      } catch (criticalError) {
        log.error('❌ Erreur critique lors du fallback final:', criticalError);

        // Dernier recours : fermer l'application
        app.quit();
      }
    }, 3000); // 3 secondes de délai
  }
}

/**
 * Créer la fenêtre principale de connexion
 */
async function createMainWindow() {
  try {
    log.info('Création de la fenêtre principale...');

    // Initialiser les gestionnaires si pas déjà fait
    if (!windowManager) windowManager = new WindowManager();

    const mainWindow = await windowManager.createMainWindow();

    // Configurer l'instance de connexion selon la découverte
    if (serverDiscovery && serverDiscovery.getSelectedServer()) {
      const selectedServer = serverDiscovery.getSelectedServer();
      log.info(`🎯 Configuration avec serveur sélectionné: ${selectedServer.url} (${selectedServer.type})`);

      // Configurer le gestionnaire de connexion avec le serveur découvert
      if (connectionManager && connectionManager.setPreferredServer) {
        connectionManager.setPreferredServer(selectedServer.url, selectedServer.type === 'local');
      }
    }

    // Afficher l'interface de connexion par défaut
    await windowManager.showLoginView();

    log.info('Fenêtre principale créée avec succès');
    return mainWindow;
  } catch (error) {
    log.error('Erreur lors de la création de la fenêtre principale:', error);
    throw error;
  }
}

// Gestionnaires d'événements IPC

/**
 * Gestionnaire pour l'authentification Odoo 13 avec serveur découvert automatiquement
 * IMPLÉMENTATION SPÉCIALISÉE AVEC DÉCOUVERTE AUTOMATIQUE
 */
ipcMain.handle('authenticate-odoo', async (event, credentials) => {
  try {
    log.info(`🚀 === DÉBUT AUTHENTIFICATION ODOO 13 ===`);
    log.info(`👤 Utilisateur: ${credentials.email}`);

    // NOUVEAU: Utiliser le serveur découvert automatiquement ou fallback
    let serverUrl;

    // Priorité 1: Serveur découvert automatiquement
    if (serverDiscovery && serverDiscovery.getSelectedServer()) {
      const selectedServer = serverDiscovery.getSelectedServer();
      serverUrl = selectedServer.url;
      log.info(`🎯 Utilisation du serveur découvert: ${serverUrl} (${selectedServer.type})`);
    }
    // Priorité 2: Serveur préféré du connection manager
    else if (connectionManager && connectionManager.getPreferredServer()) {
      const preferredServer = connectionManager.getPreferredServer();
      serverUrl = preferredServer.url;
      log.info(`🎯 Utilisation du serveur préféré: ${serverUrl} (${preferredServer.isLocal ? 'local' : 'distant'})`);
    }
    // Fallback: Méthode traditionnelle
    else {
      if (credentials.instance === 'local') {
        serverUrl = await connectionManager.findAvailableLocalServer();
        if (!serverUrl) {
          throw new Error('Aucun serveur local disponible');
        }
      } else {
        serverUrl = connectionManager.getRemoteServerUrl();
      }
      log.info(`🔄 Fallback vers méthode traditionnelle: ${serverUrl}`);
    }

    log.info(`📡 Serveur cible: ${serverUrl}`);

    // Effectuer l'authentification Odoo 13 directe
    const authResult = await odooAuth.authenticateOdoo13Direct(
      credentials.email,
      credentials.password,
      serverUrl
    );

    if (authResult.success) {
      log.info(`Authentification réussie pour l'utilisateur: ${authResult.name}`);

      // Préparer les données pour l'interface Odoo
      const odooData = {
        serverUrl: serverUrl,
        sessionId: authResult.sessionId,
        userId: authResult.userId,
        userName: authResult.name,
        dbName: authResult.dbName || 'ligne-digitale'
      };

      // Afficher l'écran de chargement dans la même fenêtre
      try {
        await windowManager.showSplashView();

        // Attendre 8 secondes pour que l'utilisateur voie l'écran de chargement complet
        log.info('Attente de la completion de l\'écran de chargement (8 secondes)...');
        await new Promise(resolve => setTimeout(resolve, 8000));

        // Charger automatiquement l'interface Odoo
        log.info('Chargement automatique de l\'interface Odoo...');
        await windowManager.showOdooView(odooData);

      } catch (splashError) {
        log.warn('Erreur lors de l\'affichage de l\'écran de chargement:', splashError.message);
        // Essayer de charger Odoo directement
        try {
          await windowManager.showOdooView(odooData);
        } catch (odooError) {
          log.error('Erreur lors du chargement d\'Odoo:', odooError.message);
        }
      }

      return {
        success: true,
        data: odooData
      };
    } else {
      log.error(`Échec de l'authentification: ${authResult.error}`);
      return {
        success: false,
        error: authResult.error
      };
    }
  } catch (error) {
    log.error('Erreur lors de l\'authentification:', error);
    return {
      success: false,
      error: error.message || 'Erreur inconnue lors de l\'authentification'
    };
  }
});

/**
 * Gestionnaire pour afficher l'écran de chargement
 */
ipcMain.handle('show-splash-screen', async () => {
  try {
    await windowManager.showSplashView();
    return { success: true };
  } catch (error) {
    log.error('Erreur lors de l\'affichage de l\'écran de chargement:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour masquer l'écran de chargement
 */
ipcMain.handle('hide-splash-screen', async () => {
  try {
    await windowManager.hideSplashScreen();
    return { success: true };
  } catch (error) {
    log.error('Erreur lors du masquage de l\'écran de chargement:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour afficher l'interface Odoo
 */
ipcMain.handle('show-odoo-interface', async (event, odooData) => {
  try {
    log.info('Chargement de l\'interface Odoo...');

    // Dans le mode fenêtre unique, on change juste la vue
    await windowManager.showOdooView(odooData);

    log.info('Interface Odoo affichée dans la fenêtre principale');
    return { success: true };
  } catch (error) {
    log.error('Erreur lors du chargement de l\'interface Odoo:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour la déconnexion Odoo - redirection vers interface de connexion
 * NOUVEAU: Gestion spécialisée des déconnexions Odoo
 */
ipcMain.handle('handle-odoo-logout', async (event) => {
  try {
    log.info('🚪 === GESTION DÉCONNEXION ODOO ===');
    log.info('Redirection vers l\'interface de connexion de l\'application');

    // Utiliser la méthode spécialisée du window manager
    await windowManager.handleOdooLogout();

    log.info('✅ Déconnexion Odoo gérée avec succès');
    return { success: true };
  } catch (error) {
    log.error('❌ Erreur lors de la gestion de déconnexion Odoo:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour tester la connexion
 */
ipcMain.handle('test-connection', async (event, serverUrl) => {
  try {
    const isAvailable = await connectionManager.testConnection(serverUrl);
    return { success: true, available: isAvailable };
  } catch (error) {
    log.error(`Erreur lors du test de connexion vers ${serverUrl}:`, error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour obtenir la liste des serveurs disponibles
 */
ipcMain.handle('get-available-servers', async () => {
  try {
    const servers = await connectionManager.getAvailableServers();
    return { success: true, servers };
  } catch (error) {
    log.error('Erreur lors de la récupération des serveurs:', error);
    return { success: false, error: error.message };
  }
});

/**
 * NOUVEAU: Gestionnaire pour la découverte automatique des serveurs
 */
ipcMain.handle('discover-servers', async () => {
  try {
    log.info('🔍 Lancement de la découverte des serveurs via IPC');

    if (!serverDiscovery) {
      serverDiscovery = new ServerDiscovery();
    }

    const result = await serverDiscovery.discoverBestServer();
    return result;
  } catch (error) {
    log.error('❌ Erreur lors de la découverte des serveurs:', error);
    return { success: false, error: error.message };
  }
});

/**
 * NOUVEAU: Gestionnaire pour relancer la découverte des serveurs
 */
ipcMain.handle('retry-server-discovery', async () => {
  try {
    log.info('🔄 Relance de la découverte des serveurs');

    if (serverDiscovery) {
      serverDiscovery.reset();
    }

    // Relancer la découverte
    await startServerDiscovery();

    return { success: true };
  } catch (error) {
    log.error('❌ Erreur lors de la relance de découverte:', error);
    return { success: false, error: error.message };
  }
});

/**
 * NOUVEAU: Gestionnaire pour obtenir le serveur préféré découvert
 */
ipcMain.handle('get-preferred-server', async () => {
  try {
    let preferredServer = null;

    // Priorité 1: Serveur découvert automatiquement
    if (serverDiscovery && serverDiscovery.getSelectedServer()) {
      const selectedServer = serverDiscovery.getSelectedServer();
      preferredServer = {
        url: selectedServer.url,
        type: selectedServer.type,
        source: 'discovery'
      };
    }
    // Priorité 2: Serveur préféré du connection manager
    else if (connectionManager && connectionManager.getPreferredServer()) {
      const preferred = connectionManager.getPreferredServer();
      preferredServer = {
        url: preferred.url,
        type: preferred.isLocal ? 'local' : 'remote',
        source: 'manager'
      };
    }

    return { success: true, preferredServer };
  } catch (error) {
    log.error('❌ Erreur lors de la récupération du serveur préféré:', error);
    return { success: false, error: error.message };
  }
});

// Gestionnaires d'utilitaires
ipcMain.handle('get-app-version', () => app.getVersion());
ipcMain.handle('close-app', () => app.quit());
ipcMain.handle('minimize-app', () => BrowserWindow.getFocusedWindow()?.minimize());
ipcMain.handle('maximize-app', () => {
  const window = BrowserWindow.getFocusedWindow();
  if (window?.isMaximized()) {
    window.unmaximize();
  } else {
    window?.maximize();
  }
});

// Événements de l'application

/**
 * Événement déclenché quand Electron est prêt
 * NOUVEAU: Démarrage avec splash screen et découverte automatique
 */
app.whenReady().then(async () => {
  await initializeApp();

  // NOUVEAU: Démarrer avec la fenêtre splash au lieu de la fenêtre principale
  await createSplashWindow();

  // Sur macOS, recréer une fenêtre quand l'icône du dock est cliquée
  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      // Si aucune fenêtre n'est ouverte, relancer le processus complet
      await createSplashWindow();
    }
  });
});

/**
 * Quitter quand toutes les fenêtres sont fermées
 */
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

/**
 * Événement avant la fermeture de l'application
 */
app.on('before-quit', () => {
  log.info('Fermeture de l\'application Edara');
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  log.error('Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Promesse rejetée non gérée:', reason);
});
