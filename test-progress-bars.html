<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des barres de progression - Edara</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f0f0;
        }

        .test-container {
            display: flex;
            gap: 40px;
            justify-content: center;
            align-items: flex-start;
            min-height: 80vh;
        }

        .test-section {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            width: 400px;
        }

        .test-section h2 {
            margin-top: 0;
            color: #333;
            text-align: center;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        /* Styles de splash-final.html - AMÉLIORÉS */
        .final-style {
            --progress-bar-background: #333333;
            --progress-bar-fill: #FFFFFF;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
        }

        .final-style .progress-container {
            width: 100%;
            max-width: 280px;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .final-style .progress-bar {
            width: 100%;
            height: 3px;
            background-color: var(--progress-bar-background);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
            opacity: 1;
            transform: scaleX(1);
            transition: opacity 0.5s ease, transform 0.5s ease, background-color 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .final-style .progress-fill {
            height: 100%;
            width: 75%;
            background: linear-gradient(90deg, var(--progress-bar-fill), rgba(255, 255, 255, 0.8));
            border-radius: 2px;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s ease;
            position: relative;
        }

        .final-style .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
            border-radius: 0 2px 2px 0;
        }

        .final-style .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 40%,
                rgba(255, 255, 255, 0.4) 50%,
                rgba(255, 255, 255, 0.2) 60%,
                transparent 100%);
            animation: progress-shine 2s infinite;
            transform: translateX(-100%);
            border-radius: 2px;
            z-index: 1;
        }

        .final-style .loading-text {
            font-size: 14px;
            font-weight: 400;
            color: var(--secondary-text-color);
            text-align: center;
            line-height: 1.4;
            letter-spacing: 0.2px;
            min-height: 20px;
        }

        @keyframes progress-shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Styles de splash.html (harmonisés) */
        .splash-style {
            --progress-bar-background: #333333;
            --progress-bar-fill: #FFFFFF;
            --text-color: #F8F9FA;
        }

        .splash-style .progress-container {
            width: 100%;
            margin-top: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
        }

        .splash-style .progress-bar {
            width: 120px;
            height: 2px;
            background-color: var(--progress-bar-background);
            border-radius: 1px;
            overflow: hidden;
            margin-bottom: 10px;
            position: relative;
            opacity: 1;
            transform: scaleX(1);
            transition: opacity 0.5s ease, transform 0.5s ease, background-color 0.3s ease;
        }

        .splash-style .progress-bar-fill {
            width: 75%;
            height: 100%;
            background-color: var(--progress-bar-fill);
            transition: width 0.3s ease-out, background-color 0.3s ease;
        }

        .splash-style .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
            animation: progress-shine 1.5s infinite;
            transform: translateX(-100%);
            opacity: 0.5;
            transition: background 0.3s ease;
        }

        .demo-controls {
            text-align: center;
            margin-top: 20px;
        }

        .demo-controls button {
            background: #124559;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }

        .demo-controls button:hover {
            background: #0f3a4a;
        }

        .status {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #333;">Test de comparaison des barres de progression</h1>

    <div class="test-container">
        <div class="test-section final-style">
            <h2>splash-final.html - AMÉLIORÉ</h2>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="finalProgress"></div>
                </div>
                <div class="loading-text">Chargement des composants...</div>
            </div>
            <div class="status">Largeur: 280px max, Hauteur: 3px</div>
            <div class="status">Animation de brillance: ✓</div>
            <div class="status">Design professionnel: ✓</div>
        </div>

        <div class="test-section splash-style">
            <h2>splash.html - AMÉLIORÉ</h2>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-bar-fill" id="splashProgress"></div>
                </div>
                <div class="loading-text">Vérification de la connexion...</div>
            </div>
            <div class="status">Largeur: 280px max, Hauteur: 3px</div>
            <div class="status">Animation de brillance: ✓</div>
            <div class="status">Design professionnel: ✓</div>
        </div>
    </div>

    <div class="demo-controls">
        <button onclick="setProgress(25)">25%</button>
        <button onclick="setProgress(50)">50%</button>
        <button onclick="setProgress(75)">75%</button>
        <button onclick="setProgress(100)">100%</button>
        <button onclick="animateProgress()">Animation</button>
    </div>

    <script>
        function setProgress(percent) {
            document.getElementById('finalProgress').style.width = percent + '%';
            document.getElementById('splashProgress').style.width = percent + '%';
        }

        function animateProgress() {
            let progress = 0;
            const interval = setInterval(() => {
                progress += 2;
                setProgress(progress);
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => setProgress(0), 1000);
                }
            }, 50);
        }

        // Animation automatique au chargement
        window.addEventListener('load', () => {
            setTimeout(animateProgress, 1000);
        });
    </script>
</body>
</html>
